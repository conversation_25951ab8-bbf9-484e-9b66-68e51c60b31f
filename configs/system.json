{"database": {"knowledge_graph_db": "~/.sutra/data/knowledge_graph.db", "embeddings_db": "~/.sutra/data/knowledge_graph_embeddings.db", "connection_timeout": 60, "max_retry_attempts": 5, "batch_size": 1000, "enable_indexing": true, "create_tables": true, "enable_wal_mode": true}, "storage": {"data_dir": "~/.sutra/data", "sessions_dir": "~/.sutra/data/sessions", "file_changes_dir": "~/.sutra/data/file_changes", "file_edits_dir": "~/.sutra/data/edits", "parser_results_dir": "~/.sutra/parser_results", "models_dir": "~/.sutra/models"}, "embedding": {"model_name": "all-MiniLM-L12-v2", "model_path": "~/.sutra/models/all-MiniLM-L12-v2", "max_tokens": 230, "overlap_tokens": 30, "similarity_threshold": 0.2, "enable_optimization": false}, "parser": {"config_file": "~/.sutra/config/parsers.json", "build_directory": "~/.sutra/build"}, "web_search": {"api_key": "YOUR_WEB_SEARCH_API_KEY", "requests_per_minute": 60, "timeout": 30}, "web_scrap": {"timeout": 30, "max_retries": 3, "delay_between_retries": 1.0, "include_comments": true, "include_tables": true, "include_images": true, "include_links": true, "trafilatura_config": {}, "markdown_options": {"heading_style": "ATX", "bullets": "-", "wrap": true}}, "logging": {"level": "DEBUG", "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}", "log_file": "~/.sutra/logs/sutraknowledge.log"}, "llm": {"provider": "aws", "llama_model_id": "meta/llama-4-maverick-17b-128e-instruct-maas", "gemini_model": "gemini-2.5-flash", "aws": {"model_id": "us.anthropic.claude-sonnet-4-20250514-v1:0", "access_key_id": "********************", "secret_access_key": "EBLJIAeYRTfQREZ2mOrSSH3Xc9qdC8HNjUj1xpdR", "region": "us-east-2"}, "anthropic": {"model_id": "claude-sonnet-4-20250514", "api_key": "YOUR_ANTHROPIC_API_KEY"}, "gcp": {"api_key": "", "project_id": "YOUR_GCP_PROJECT_ID", "location": "YOUR_GCP_LOCATION", "llm_endpoint": "YOUR_GCP_LLM_ENDPOINT"}, "superllm": {"api_endpoint": "http://localhost:8000", "firebase_token": "", "default_model": "gpt-3.5-turbo", "default_provider": "openai"}}}