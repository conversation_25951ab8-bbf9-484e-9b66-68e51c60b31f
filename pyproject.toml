[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend="setuptools.build_meta"

[project]
name="sutrakit"
version="0.1.6"
description="A comprehensive AI-powered code analysis and automation CLI tool"
readme="README.md"
license = {text="MIT"}
authors = [
    {name="Your Name", email="<EMAIL>"}
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Software Development :: Code Generators",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
keywords = ["ai", "code-analysis", "automation", "cli", "agent"]
requires-python=">=3.8"
dependencies = [
    "python-dotenv==1.1.0",
    "pydantic==2.11.5",
    "loguru==0.7.3",
    "onnxruntime==1.22.0",
    "numpy==2.2.6",
    "tokenizers==0.21.1",
    "sqlite-vec==0.1.6",
    "boto3==1.38.36",
    "botocore==1.38.36",
    "google-auth==2.40.3",
    "google-cloud-aiplatform==1.98.0",
    "anthropic==0.55.0",
    "google-genai==1.21.1",
    "tqdm==4.66.4",
    "cryptography==44.0.0",
    "requests==2.32.3",
    "xmltodict==0.14.2",
    "trafilatura==2.0.0",
    "markdownify==1.1.0",
    "dateparser==1.2.2",
    "tree-sitter==0.20.1",
    "PyYAML==6.0.1",
    "setuptools==80.9.0",
    "prompt_toolkit==3.0.48",
]

[project.urls]
Homepage="https://github.com/sutragraph/sutracli"
Repository="https://github.com/sutragraph/sutracli"
Issues="https://github.com/sutragraph/sutracli/issues"

[project.scripts]
sutrakit="cli.main:main"
sutrakit-setup="sutrakit.setup_directories:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"
