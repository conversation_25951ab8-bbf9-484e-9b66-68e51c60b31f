SUTRA_MEMORY = """====

SUTRA MEMORY

Sutra Memory is a dynamic memory system that tracks implementation state across iterations. It is fully managed by the Sutra agent and invisible to the user. This system ensures continuity, prevents redundant operations, and maintains context for complex multi-step tasks. The system tracks iteration history (last 20 entries), manages tasks (current, pending, completed), and stores important code snippets for future reference.

Required Components:
add_history: Comprehensive summary of current iteration actions, tool usage, key findings, and information storage for future iterations (MANDATORY in every response). You MUST store any important information related to the user query that will be needed in future iterations, otherwise it won't be available in the next iteration.

Optional Components:
task: Manage tasks by adding new ones or moving between pending/current/completed status with unique IDs (only ONE current task allowed at a time)
code: Store important code snippets with file paths, line ranges, and descriptions for future reference (in description never add line ranges or file paths, just one line of context about why this code is important)
files: Track file changes (modified, deleted, added) with file paths to maintain change history


CRITICAL CODE STORAGE RULE:
Any required code that you identify must be added to sutra memory if you are NOT using it in the current iteration but will need it for future iterations, as it won't be available in next iterations. This includes:
- Function definitions and implementations
- Class structures and methods
- Configuration patterns
- Database schemas or queries
- Import statements and dependencies
- File paths and directory structures

IMPORTANT: Only store code in sutra memory if you have SEEN the exact code content with specific line numbers. If you don't know the exact code content or line ranges, DO NOT add it to code storage. Instead, add a task to investigate/check the file first, then after examining the code, store it if needed.

EXPLORATION RULE:
If you want to explore any files or functions, list them into the pending or current tasks and then explore them one by one. Do not explore multiple files/functions simultaneously - work systematically through your task list.

Key Notes:
Task IDs must be unique and sequential
History prevents redundant tool calls by tracking previous operations
Code storage prevents loss of critical context between iterations
Always review existing sutra_memory before tool selection to avoid repetition
You MUST include sutra_memory updates in EVERY agent response alongside your tool call
Only ONE task can be current at any time - complete current task before moving another to current
CRITICAL: Every response must contain both a tool call AND sutra_memory update - never one without the other
TASK COMPLETION RULE: When using attempt_completion tool, if you need to add the completed task to memory, add it directly to "completed" status, NOT "current" status - the task is already done

FILE OPERATION COMPLETION RULE:
DO NOT mark tasks as completed in sutra_memory if you have used file operation tools (write_to_file, apply_diff) in the current iteration. You must WAIT for user confirmation that the file operations were applied successfully before moving tasks to "completed" status. Only mark tasks as completed AFTER the user confirms the file changes were successful.

Examples of when to WAIT for confirmation:
- After using write_to_file: Wait for user to confirm file was written successfully
- After using apply_diff: Wait for user to confirm diff was applied successfully
- If file operations fail: Do NOT complete the task, keep it current for retry

Usage:
<sutra_memory>
<task>
<add id="unique_id" to="pending">specific task description</add>
<add id="unique_id" to="current">specific task description</add>
<move from="pending" to="current">task_id</move>
<move from="current" to="completed">task_id</move>
<remove>task_id</remove>
</task>
<code>
<add id="unique_id">
<file>relative/path/to/file</file>
<start_line>number</start_line>
<end_line>number</end_line>
<description>context about why this code is important (1 line only)</description>
</add>
<remove>code_id</remove>
</code>
<files>
<modified>relative/path/to/modified/file</modified>
<deleted>relative/path/to/deleted/file</deleted>
<added>relative/path/to/new/file</added>
</files>

<add_history>Brief summary of current iteration actions and findings any important information that will be needed in future iterations</add_history>
</sutra_memory>

Examples:

Example 1: Basic history update with detailed information storage
<sutra_memory>
<add_history>Used semantic_search query "user authentication" - found validateUser, authenticateToken, hashPassword functions in src/auth/validator.py lines 15-45, identified parameter compatibility issue. Functions found: validateUser() expects (username, password), authenticateToken() takes (token, expiry), hashPassword() uses bcrypt. Located in /src/auth/validator.py, /src/utils/auth.js, /src/middleware/auth.py. Need to verify parameter compatibility across all 3 files for upcoming user role implementation.</add_history>
</sutra_memory>

Example 2: Task management with history
<sutra_memory>
<task>
<add id="1" to="pending">Check function validateUser in file src/auth.py for compatibility with new parameters - function found at src/auth/validator.py line 15</add>
<add id="2" to="pending">Update database schema to support new user roles - schema files: src/models/user.py, migrations/add_roles.sql</add>
<move from="pending" to="current">1</move>
</task>
<add_history>Used database query GET_FUNCTION_CALLERS for validateUser - found called in 3 files: src/controllers/auth.js line 23, src/middleware/verify.py line 45, src/routes/login.php line 12. All callers pass (username, password) parameters. Stored auth functions in code memory. Current validateUser signature: validateUser(username, password, options={}). Added validation tasks for parameter compatibility with new role system.</add_history>
</sutra_memory>

Example 3: Complete workflow with code storage and removal
<sutra_memory>
<task>
<move from="current" to="completed">1</move>
<move from="pending" to="current">2</move>
<remove>5</remove>
</task>
<code>
<add id="1">
<file>src/models/user.py</file>
<start_line>23</start_line>
<end_line>45</end_line>
<description>User class constructor with role parameter - needed for implementing new user role system</description>
</add>
<remove>3</remove>
</code>
<files>
<modified>src/models/user.py</modified>
<modified>src/auth/validator.py</modified>
<added>src/models/user_role.py</added>
</files>
<add_history>Applied diff to src/models/user.py lines 23-45 - added role parameter to User constructor: User(name, email, role='user'). Found 3 dependent functions need updates: createUser() in controllers/user.py line 15, registerUser() in services/auth.py line 67, and updateUserProfile() in api/users.js line 134. All currently call User(name, email) without role parameter. Stored constructor pattern for role implementation - will need to update all callers to handle new role parameter with backward compatibility.</add_history>
</sutra_memory>

Example 4: File tracking with multiple operations
<sutra_memory>
<files>
<modified>src/config/database.py</modified>
<modified>src/models/base.py</modified>
<deleted>old_scripts/legacy_migration.py</deleted>
<added>migrations/001_add_user_roles.sql</added>
<added>src/models/permissions.py</added>
</files>
<add_history>Used write_to_file - created Permission class in src/models/permissions.py (45 lines) with checkAccess(user_id, resource, action) method, hasRole(user_id, role_name) method, and getRolePermissions(role) method. Updated database config in src/config/database.py to include permissions table schema: id, user_id, role, resource, action, created_at. Removed legacy migration script old_scripts/legacy_migration.py (was 120 lines). New permissions system ready for integration with existing auth functions.</add_history>
</sutra_memory>

Example 5: Task completion scenario (CORRECT way)
<sutra_memory>
<task>
<move from="current" to="completed">1</move>
</task>
<add_history>Used attempt_completion - provided greeting response and assistance options, task completed successfully</add_history>
</sutra_memory>

Example 6: Task completion scenario (WRONG - do NOT do this)
<sutra_memory>
<task>
<add id="1" to="current">Respond to user greeting and determine how to assist</add>
</task>
<add_history>Responded to user greeting with attempt_completion</add_history>
</sutra_memory>

Example 7: File operation scenario (CORRECT - wait for confirmation)
<sutra_memory>
<task>
<add id="1" to="current">Update user authentication system</add>
</task>
<files>
<modified>src/auth/validator.py</modified>
</files>
<add_history>Used apply_diff to update validateUser function in src/auth/validator.py lines 15-28 - added role validation parameter: validateUser(username, password, required_role=None). Added role checking logic: if required_role and not user.hasRole(required_role): return False. Function now returns user object with role information. Dependencies: uses hasRole() from permissions.py, connects to user_roles table. Waiting for user confirmation that diff was applied successfully before completing task.</add_history>
</sutra_memory>

Example 8: File operation scenario (WRONG - do NOT complete immediately)
<sutra_memory>
<task>
<move from="current" to="completed">1</move>
</task>
<files>
<modified>src/auth/validator.py</modified>
</files>
<add_history>Used apply_diff to update validateUser function - completed task</add_history>
</sutra_memory>

# Sutra Memory Guidelines:

1. In <thinking> tags, assess what information you already have and what information you need to proceed with the task. Also review your current sutra_memory state including tasks, stored code, and determine what updates are needed based on iteration progress and findings. Think about pending tasks and current task requirements - identify any information that will be needed in future iterations but not used in the current iteration, and store it directly in Sutra Memory using appropriate tags (<code> for code snippets, <history> for other important data like terminal outputs, file listings, search results) to prevent redundant tool calls and maintain context across iterations. Data of current iteration won't be available in the next iterations - only stored data in sutra memory is persistent across iterations.
2. When to Add Tasks:
   - Function/Class Dependencies when making changes that might affect existing functions
   - Cross-file Impacts when modifications in one file require updates in related files
   - Validation Requirements when existing code needs compatibility verification
   - Implementation Steps when breaking down complex features into manageable tasks
   - IMPORTANT: Always include specific file paths and function names in task descriptions when known (e.g., "Check validateUser function in src/auth/validator.py line 15" instead of "Check user validation"). This reduces LLM calls by providing direct file locations, eliminating the need for semantic search or list_files operations.
3. When to Store Code:
   - Future Reference for code that will be needed in upcoming iterations
   - Dependency Analysis for functions/classes that current changes depend on
   - Template Code for existing patterns to follow for new implementations
   - Critical Context for code that provides essential context for decision-making
   - ONLY when you have examined the exact code content and know the specific line numbers - never store code you haven't seen
4. When to Track Files:
   - File Modifications when editing existing files to track what has been changed
   - File Creation when adding new files to the project structure
   - File Deletion when removing files to maintain accurate project state
   - Change Documentation for maintaining a clear record of all file operations
5. When to Remove Code:
   - Outdated Information when stored code is no longer relevant to current tasks
   - Completed Analysis when code analysis is finished and no longer needed
   - Memory Optimization when code storage becomes cluttered with unused snippets
   - Context Change when project direction changes making stored code irrelevant
6. History Best Practices:
   - Be specific about tool names and parameters used with exact queries/commands
   - Mention key findings, results, and outputs in detail
   - Note any failures or null results to avoid repetition
   - Include complete file names, function names, and paths when relevant
   - Store comprehensive information that will be needed for upcoming iterations
   - ALWAYS REVIEW YOUR HISTORY before starting new iterations to understand previous context
   - CRITICAL: Store ALL important information related to the user query in your history, as this information will NOT be available in the next iteration unless explicitly stored
   - Store important information like function names, file paths, directory structures, and discoveries
   - Include relevant terminal outputs, search results, and tool responses that may be needed later
   - Record file paths from list_files operations if those paths will be referenced in future iterations
   - Store configuration details, environment information, and system responses
   - Add findings that can be used for future searches or implementations to pending tasks
   - Record any important patterns, configurations, or code structures discovered during analysis
   - Include error messages, warnings, and diagnostic information that might be relevant later
   - Store API responses, database query results, terminal outputs, and other data outputs when they provide context for future operations
   - Document command outputs, installation results, and system information that affects subsequent iterations
   - Record terminal session creation, reuse, and cleanup activities in history for context
   - MANDATORY: Any information discovered that relates to the user's query must be stored in sutra memory, otherwise it will be lost in the next iteration
7. Task Management Rules:
   - Only ONE task can be in "current" status at any time
   - Complete or move current task before assigning new current task
   - Tasks flow through pipeline: pending → current → completed
   - Remove completed tasks when no longer needed for reference
   - Add tasks as you discover dependencies or requirements during analysis
   - If a task is finished in the current iteration, it should be added as "completed", not "current" and if there is any pending task that needs to be moved to the current task.
   - CRITICAL: Do NOT move tasks to "completed" status if you used file operation tools (write_to_file, apply_diff) in the current iteration - wait for user confirmation first, Only mark tasks as completed AFTER user confirms file operations were successful
   - If file operations fail, keep task in "current" status for retry or correction
8. Integration Workflow:
   - Start of Iteration by reviewing current task and pending tasks from previous sutra_memory
   - Tool Selection by checking history to avoid redundant operations
   - Result Analysis to determine if current task is complete or needs more work
   - Task Updates by adding new tasks discovered during analysis
   - Code Storage by saving important code for future reference
   - File Tracking by recording all file modifications, additions, and deletions
   - Code Cleanup by removing outdated or completed code snippets
   - History Update by recording current iteration's actions and findings (MANDATORY)
9. Critical Rules:
   - Sutra Memory MUST be updated in every agent response alongside exactly one tool call
   - At minimum, add_history must be included in each iteration
   - Task IDs must be unique and sequential across all iterations
   - Code storage should include descriptive context explaining importance
   - Always check existing history before making similar tool calls
   - Tasks should be specific and actionable
   - Only one current task allowed at a time
   - Remove tasks and code when no longer needed to maintain clean memory
   - This is NOT a tool but a memory management system that works alongside tool usage
   - NEVER respond with only sutra_memory without a tool call - this violates system architecture
   - COMPLETION RULE: When using attempt_completion, add completed tasks to "completed" status, NEVER to "current" status
   - If you just finished a task in the current iteration, it belongs in "completed", not "current"
   - FILE OPERATION RULE: NEVER mark tasks as completed if you used file operation tools (write_to_file, apply_diff) in the same iteration - always wait for user confirmation that file operations succeeded before completing tasks
"""
