WORKSPACE_STRUCTURE = """====

WORKS<PERSON>CE STRUCTURE

Current Workspace Directory: {current_dir}

Structure:
{workspace_structure}

This section provides a comprehensive overview of the project's directory structure, showing folders. The structure shows directories up to a limited depth, so there may be additional subdirectories not visible in this overview. This gives key insights into the project from directory names and how developers conceptualize and organize their code. The WORKSPACE STRUCTURE represents the initial state of the project and remains static throughout your session. When you make changes like adding or removing folders, these modifications are tracked separately in sutra memory, which is provided with each user interaction to keep you informed of the current project state. This dual system ensures you always have both the original project layout for reference and current modifications for accurate decision-making.

Note: For exploring specific directories and viewing files, use the list_files tool instead of `ls` command, as it is optimized and faster than traditional shell commands."""
