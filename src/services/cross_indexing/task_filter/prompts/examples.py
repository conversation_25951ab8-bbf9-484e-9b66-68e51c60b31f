"""
Task Filter Examples

Provides examples of task filtering and deduplication operations.
"""

TASK_FILTER_EXAMPLES = """# Task Filtering Examples

## Example 1: Merging Similar Search Pattern Tasks

**Input Tasks:**
- Task 2: Use search_keyword tool with pattern 'require\\('axios'\\)|import.*from.*'axios'|import.*axios|axios' and regex=true to find axios import statements. Look for HTTP client library imports enabling requests to other services.
- Task 5: Use search_keyword tool with pattern 'require\\('axios'\\)|import.*from.*'axios'|import.*axios' and regex=true to find axios import statements. Look for HTTP client library imports enabling requests to other services.

**Analysis:** Task 5 is missing '|axios' from the pattern but otherwise identical.

**Output:**
```xml
<sutra_memory>
<task>
<add id="1" to="pending">Use search_keyword tool with pattern 'require\\('axios'\\)|import.*from.*'axios'|import.*axios|axios' and regex=true to find axios import statements. Look for HTTP client library imports enabling requests to other services.</add>
</task>
</sutra_memory>
```

## Example 2: Merging Multiple Package Search Tasks

**Input Tasks:**
- Task 3: Use search_keyword tool with pattern 'express' to find Express.js usage
- Task 7: Use search_keyword tool with pattern 'require\\('express'\\)|import.*express' to find Express imports
- Task 12: Use database tool to analyze Express.js server setup in main files

**Analysis:** All tasks relate to Express.js analysis but use different approaches.

**Output:**
```xml
<sutra_memory>
<task>
<add id="1" to="pending">Use search_keyword tool with pattern 'require\\('express'\\)|import.*express|express' and regex=true to find Express.js imports and usage patterns. Look for web server framework imports and setup.</add>
<add id="2" to="pending">Use database tool to analyze Express.js server configuration and setup in main application files. Focus on server initialization and middleware setup.</add>
</task>
</sutra_memory>
```

## Example 3: Preserving Distinct Tasks

**Input Tasks:**
- Task 4: Use search_keyword tool with pattern 'socket\\.io' to find Socket.IO usage
- Task 8: Use search_keyword tool with pattern 'ws|websocket' to find WebSocket usage
- Task 11: Use database tool to analyze real-time communication setup

**Analysis:** Different technologies (Socket.IO vs WebSocket) should remain separate.

**Output:**
```xml
<sutra_memory>
<task>
<add id="1" to="pending">Use search_keyword tool with pattern 'socket\\.io|require\\('socket\\.io'\\)|import.*socket\\.io' and regex=true to find Socket.IO imports and usage. Look for real-time communication library setup.</add>
<add id="2" to="pending">Use search_keyword tool with pattern 'ws|websocket|require\\('ws'\\)|import.*ws' and regex=true to find WebSocket imports and usage. Look for WebSocket client/server implementations.</add>
<add id="3" to="pending">Use database tool to analyze real-time communication setup and configuration in application files. Focus on Socket.IO and WebSocket implementations.</add>
</task>
</sutra_memory>
```"""
