"""
Code Manager Guidelines

Rules, guidelines, and best practices for code storage management in Sutra memory.
"""

from .code_manager_objective import CODE_MANAGER_OBJECTIVE
from .code_manager_capabilities import CODE_MANAGER_CAPABILITIES
from .code_manager_rules import CODE_MANAGER_RULES
from .code_manager_output_format import CODE_MANAGER_OUTPUT_FORMAT
from .code_manager_examples import CODE_MANAGER_EXAMPLES

__all__ = [
    "CODE_MANAGER_OBJECTIVE",
    "CODE_MANAGER_CAPABILITIES", 
    "CODE_MANAGER_RULES",
    "CODE_MANAGER_OUTPUT_FORMAT",
    "CODE_MANAGER_MEMORY",
    "CODE_MANAGER_EXAMPLES"
]
